import axiosInstance from '../lib/axios';

export const getExamApplicant = async (examId:number,page: number = 1, limit: number = 10) => {
  try {
    const response = await axiosInstance.get(`/examApplication/${examId}?page=${page}&limit=${limit}`, {
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get questions: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const downloadExamApplicantsExcel = async (examId: number) => {
  try {
    const response = await axiosInstance.get(`/export/exam-applicants/${examId}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
      responseType: 'blob',
    });

    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', `exam-applicants-${examId}.xlsx`);
    document.body.appendChild(link);
    link.click();

    link.parentNode?.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return true;
  } catch (error: any) {
    console.error('Error downloading Excel file:', error);
    throw new Error(`Failed to download Excel file: ${error.message}`);
  }
};