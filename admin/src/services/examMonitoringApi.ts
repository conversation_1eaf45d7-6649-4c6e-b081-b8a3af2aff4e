import axiosInstance from '@/lib/axios';

export interface ExamPhoto {
  id: string;
  studentId: string;
  photoUrl: string;
  capturedAt: string;
}

export interface ExamPhotosResponse {
  success: boolean;
  data?: ExamPhoto[];
  error?: string;
}

export const getExamPhotos = async (examId: number): Promise<ExamPhotosResponse> => {
  try {
    const response = await axiosInstance.get(`/exam-monitoring/photos/exam/${examId}`);
    return {
      success: true,
      data: response.data.data,
    };
  } catch (error: any) {
    console.error('Error fetching exam photos:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch photos',
    };
  }
};

export const getStudentExamPhotos = async (studentId: string, examId: number): Promise<ExamPhotosResponse> => {
  try {
    const response = await axiosInstance.get(`/exam-monitoring/photos/${studentId}/${examId}`);
    return {
      success: true,
      data: response.data.data,
    };
  } catch (error: any) {
    console.error('Error fetching student exam photos:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch photos',
    };
  }
};
