import axiosInstance from '../lib/axios';
import { ExamInput } from '../lib/types';

export const getExams = async (page: number = 1, limit: number = 10) => {
  try {
    const response = await axiosInstance.get(`/exams?page=${page}&limit=${limit}`, {
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get questions: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const createExam = async (data: ExamInput) => {
  try {
    const response = await axiosInstance.post('/exams', data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create Exam: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const updateExam = async (id: number, data: Partial<ExamInput>) => {
  try {
    const response = await axiosInstance.put(`/exams/${id}`, data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to update exam: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const deleteExam = async (id: number) => {
  try {
    const response = await axiosInstance.delete(`/exams/${id}`,{
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete exam: ${error.response?.data?.message || error.message}`,
    };
  }
};
