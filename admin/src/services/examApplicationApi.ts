import { axiosInstance } from '@/lib/axios';

interface ExamApplication {
  id: string;
  examId: number;
  classId: string;
  createdAt: string;
  exam: { exam_name: string };
  classes: { firstName: string; lastName: string; className: string | null };
}

interface PaginationResponse {
  applications: ExamApplication[];
  total: number;
  currentPage: number;
  totalPages: number;
}

export const getAllExamApplications = async (
  page: number = 1,
  limit: number = 10,
  search?: string,
  filterType?: string
): Promise<PaginationResponse> => {
  try {
    const params: Record<string, any> = { page, limit };
    if (search) {
      params.search = search;
    }
    if (filterType) {
      params.filterType = filterType;
    }
    const response = await axiosInstance.get('/exam-applications', {
      params,
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || 'Failed to fetch exam applications');
  }
};
