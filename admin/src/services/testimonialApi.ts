import { axiosInstance } from '@/lib/axios';
import { Testimonial } from '@/lib/types';

interface TestimonialResponse {
  testimonials: Testimonial[];
  total: number;
  pages: number;
  currentPage: number;
}

export const getTestimonials = async (
  page: number = 1,
  limit: number = 10,
  status?: 'PENDING' | 'APPROVED' | 'REJECTED'
): Promise<TestimonialResponse> => {
  try {
    const response = await axiosInstance.get('/testimonials', {
      params: { page, limit, status }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch testimonials: ${error.message}`);
  }
};

export const updateTestimonialStatus = async (
  id: string,
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
): Promise<Testimonial> => {
  try {
    const response = await axiosInstance.patch(`/testimonials/${id}/status`, { status });

    return response.data.data || response.data;
  } catch (error: any) {
    console.error('Error updating testimonial status:', error);
    throw new Error(error.response?.data?.message || `Failed to update testimonial status: ${error.message}`);
  }
};

export const deleteTestimonial = async (id: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/testimonials/admin/${id}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to delete testimonial: ${error.message}`);
  }
};
