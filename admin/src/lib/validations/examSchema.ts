import { z } from "zod";

export const examSchema = z
  .object({
    exam_name: z.string().min(1, "Exam name is required"),
    start_date: z
      .string()
      .min(1, "Start date is required")
      .refine((val) => !isNaN(new Date(val).getTime()), {
        message: "Invalid start date",
      }),
    duration: z.number().min(1, "Duration must be at least 1 minute"),
    marks: z.number().min(1, "Marks cannot be negative"),
    total_student_intake: z
      .number()
      .min(1, "Total students should be greater than 0"),
    total_questions: z
      .number()
      .min(1, "Total questions should be greater than 0"),
    level: z.enum(["easy", "medium", "hard"]),
    coins_required: z
      .number()
      .min(0, "Max questions per class is required and cannot be negative"),
    exam_type: z.enum(["CLASSES", "STUDENTS"]).optional(),
    start_registration_date: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val) return true;
          const date = new Date(
            val.includes("Z") || val.includes("+") ? val : `${val}:00.000Z`
          );
          return !isNaN(date.getTime());
        },
        { message: "Invalid start registration date" }
      )
      .refine(
        (val) => {
          if (!val) return true;
          const regDate = new Date(
            val.includes("Z") || val.includes("+") ? val : `${val}:00.000Z`
          );
          const now = new Date();
          return regDate > now;
        },
        { message: "Start registration date must be in the future" }
      )
      .transform((val) => {
        if (!val) return undefined;
        const date = new Date(
          val.includes("Z") || val.includes("+") ? val : `${val}:00.000Z`
        );
        return isNaN(date.getTime()) ? undefined : date.toISOString();
      }),
  })
  .refine(
    (data) => {
      if (!data.start_registration_date) return true; 
      const regDate = new Date(data.start_registration_date);
      const startDate = new Date(data.start_date);
      return regDate < startDate;
    },
    {
      message: "Start registration Apply date must be before start date",
      path: ["start_registration_date"],
    }
  );

export type ExamFormValues = z.infer<typeof examSchema>;
