import { z } from "zod";

// Profile validation (includes Basic Information and About Information)
export const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, "Username must be at least 2 characters.")
    .max(30, "Username must be less than 30 characters.")
    .optional()
    .or(z.literal('')),

  firstName: z
    .string()
    .min(2, "First name must be at least 2 characters.")
    .optional()
    .or(z.literal('')),

  lastName: z
    .string()
    .min(2, "Last name must be at least 2 characters.")
    .optional()
    .or(z.literal('')),

  className: z
    .string()
    .min(2, "Class name must be at least 2 characters.")
    .optional()
    .or(z.literal('')),

  email: z
    .string()
    .email('Please enter a valid email address')
    .optional()
    .or(z.literal('')),

  contactNo: z
    .string()
    .min(10, "Contact must be at least 10 digits.")
    .max(10, "Contact must be exactly 10 digits.")
    .optional()
    .or(z.literal('')),

  isVerified: z.boolean().optional(),

  birthDate: z
    .string()
    .optional()
    .or(z.literal('')),

  tutorBio: z
    .string()
    .min(100, "Tutor Bio must be at least 100 characters.")
    .optional()
    .or(z.literal('')),

  catchyHeadline: z
    .string()
    .min(10, "Headline must be at least 10 characters.")
    .optional()
    .or(z.literal('')),
});

// Education 
export const educationItemSchema = z.object({
  id: z.string().optional(),
  isDegree: z.boolean().optional(),
  degree: z.string().min(1, "Degree is required."),
  university: z.string().min(2, "University is required."),
  passoutYear: z.string().regex(/^\d{4}$/, "Enter a valid year (e.g., 2022)."),
  degreeType: z.string().min(2, "Degree type is required."),
});

// Experience
export const experienceItemSchema = z.object({
  id: z.string().optional(),
  isExperience: z.boolean().optional(),
  title: z.string().min(2, "Experience title is required."),
  from: z.string().min(1, "Start date is required."),
  to: z.string().min(1, "End date is required."),
});

// Certificate 
export const certificateItemSchema = z.object({
  id: z.string().optional(),
  isCertificate: z.boolean().optional(),
  title: z.string().min(2, "Certificate title is required."),
  certificateUrl: z.string().min(1, "Certificate file is required."),
});

export const educationFormSchema = z.object({
  education: z.array(educationItemSchema),
});

export const experienceFormSchema = z.object({
  experience: z.array(experienceItemSchema),
});

export const certificateFormSchema = z.object({
  certificates: z.array(certificateItemSchema),
});

export type ProfileFormValues = z.infer<typeof profileFormSchema>;
export type EducationFormValues = z.infer<typeof educationFormSchema>;
export type ExperienceFormValues = z.infer<typeof experienceFormSchema>;
export type CertificateFormValues = z.infer<typeof certificateFormSchema>;
export type EducationItem = z.infer<typeof educationItemSchema>;
export type ExperienceItem = z.infer<typeof experienceItemSchema>;
export type CertificateItem = z.infer<typeof certificateItemSchema>;
