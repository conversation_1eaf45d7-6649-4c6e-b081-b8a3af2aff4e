<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class TimeTableExport implements FromView, WithEvents
{
    protected $ctdata, $date, $startdate, $enddate, $timeslots;
    public function __construct ($ctdata,$date, $startdate, $enddate, $timeslots)
    {
        $this->ctdata = $ctdata;
        $this->date = $date;
        $this->startdate = $startdate;
        $this->enddate = $enddate;
        $this->timeslots = $timeslots;
    }

    public function view(): View
    {
        $params = ['ctdata'=>$this->ctdata ,'date'=>$this->date, 'startdate'=>$this->startdate, 'enddate'=>$this->enddate, 'timeslots' => $this->timeslots];
        return view('Timetable::ViewTimetable.exporttable', $params);
    }


    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()->getColumnDimension('A')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('B')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('C')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('D')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('E')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('F')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('G')->setWidth(20);
                $event->sheet->getDelegate()->getColumnDimension('H')->setWidth(20);

            },
        ];
    }
}
