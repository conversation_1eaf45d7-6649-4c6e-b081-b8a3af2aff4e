'use client';

import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import React, { useState, useEffect, Suspense } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FiUser, FiPhone, FiMail } from "react-icons/fi";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2 } from "lucide-react";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { loginUser, registerUser, continueWithEmail } from "@/services/AuthService";
import { useRouter, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

const getLoginSchema = (loginMethod: 'mobile' | 'email', isOldUser: boolean) =>
  z.object({
    contactNo: z
      .string()
      .optional()
      .refine(
        (val) => !val ||  /^\d{10}$/.test(val),
        'Please enter a valid 10-digit mobile number'
      ),
    email: z
      .string()
      .optional()
      .refine(
        (val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
        'Invalid email address'
      ),
  }).refine(
    (data) => (loginMethod === 'mobile' || isOldUser ? !!data.contactNo : !!data.email),
    { message: 'Either mobile number or email is required', path: ['contactNo', 'email'] }
  );

const registerSchema = z.object({
  firstName: z.string().min(2, 'First name is required').regex(/^[a-zA-Z]+$/, 'Invalid first name'),
  lastName: z.string().min(2, 'Last name is required').regex(/^[a-zA-Z]+$/, 'Invalid last name'),
  contactNo: z.string().regex(/^\d{10}$/, 'Please enter a valid 10-digit mobile number'),
  referralCode: z.string().optional(),
  email: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      'Invalid email address'
    ),
});

type LoginFormValues = z.infer<ReturnType<typeof getLoginSchema>>;
type RegisterFormValues = z.infer<typeof registerSchema>;

const FormErrorAlert = ({ message }: { message: string }) => {
  if (!message) return null;
  return (
    <Alert className="mb-4 border-red-500 bg-red-50 dark:bg-red-900/20">
      <AlertCircle className="h-4 w-4 text-red-500" />
      <AlertDescription className="text-red-500">{message}</AlertDescription>
    </Alert>
  );
};

function ClassesLoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLogin, setIsLogin] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [authError, setAuthError] = useState<string>("");
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [loginMethod, setLoginMethod] = useState<'mobile' | 'email'>('mobile');
  const [isOldUser, setIsOldUser] = useState(false);
  const [emailData, setEmailData] = useState<{
    firstName: string;
    lastName: string;
    contactNo: string | null;
  } | null>(null);

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(getLoginSchema(loginMethod, isOldUser)),
    defaultValues: { contactNo: '', email: '' },
    mode: 'onChange',
  });

  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      contactNo: '',
      referralCode: searchParams.get('ref') || localStorage.getItem('referralCode') || '',
      email: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (localStorage.getItem('user')) {
      router.push('/');
      setTimeout(() => {
        toast.error('You are already logged in as a tutor. Please logout first to login as a class.');
      }, 500);
    }
  }, [router]);

  useEffect(() => {
    if (localStorage.getItem('clientToken')) {
      router.push('/');
      setTimeout(() => {
        toast.error('You are already logged in as a class.');
      }, 500);
    }
  }, [router]);

  useEffect(() => {
    const refCode = searchParams.get('ref');
    if (refCode) {
      setReferralCode(refCode);
      localStorage.setItem('referralCode', refCode);
      registerForm.setValue('referralCode', refCode);
    }
  }, [searchParams, registerForm]);

  useEffect(() => {
    if (isLogin) {
      loginForm.reset({
        contactNo: loginMethod === 'mobile' ? loginForm.getValues().contactNo : '',
        email: loginMethod === 'email' ? loginForm.getValues().email : '',
      });
      loginForm.setFocus(loginMethod === 'mobile' ? 'contactNo' : 'email');
      loginForm.trigger();
    } else {
      registerForm.reset({
        firstName: '',
        lastName: '',
        contactNo: '',
        referralCode: referralCode || '',
        email: '',
      });
      registerForm.setFocus('firstName');
      registerForm.trigger();
    }
  }, [isLogin, loginMethod, isOldUser, referralCode, loginForm, registerForm]);

const handleEmailCheck = async (email: string, contactNo?: string) => {
  setIsSubmitting(true);
  setAuthError("");
  try {
    if (!contactNo) {
      // Step 1: Check email if no contact number is provided
      const response = await continueWithEmail({ email });
      if (response.success === false) {
        setAuthError(response.message || 'Email check failed');
        toast.error(response.message || 'Email check failed');
        return;
      }
      setEmailData(response.data);
      setIsOldUser(response.data.isOldUser);
      loginForm.setValue('contactNo', response.data.contactNo || '');
      loginForm.setValue('email', email);
      loginForm.trigger();

      if (!response.data.isOldUser && response.data.otpSent) {
        const redirect = searchParams.get('redirect') || '';
        const redirectParam = redirect ? `&redirect=${encodeURIComponent(redirect)}` : '';
        router.push(`/verify-otp?contactNo=${response.data.contactNo}&flow=login&email=${encodeURIComponent(email)}${redirectParam}`);
        toast.success('OTP sent successfully. Please check your phone.');
        loginForm.reset({ contactNo: '', email: '' });
        setIsOldUser(false);
        setEmailData(null);
      } else if (response.data.isOldUser) {
        toast.info('This email is not linked to a mobile number. Please enter a mobile number to proceed.');
      }
    } else {
      const response = await loginUser({
        contactNo,
        email,
      });
      if (response.success === false) {
        setAuthError(response.message || 'Authentication failed');
        toast.error(response.message || 'Authentication failed');
        return;
      }
      const redirect = searchParams.get('redirect') || '';
      const redirectParam = redirect ? `&redirect=${encodeURIComponent(redirect)}` : '';
      router.push(`/verify-otp?contactNo=${contactNo}&flow=login&email=${encodeURIComponent(email)}${redirectParam}`);
      toast.success('OTP sent successfully. Please check your phone.');
      loginForm.reset({ contactNo: '', email: '' });
      setIsOldUser(false);
      setEmailData(null);
    }
  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || 'Something went wrong';
    setAuthError(errorMessage);
    toast.error(errorMessage);
  } finally {
    
    setIsSubmitting(false);
  }
};

const onLoginSubmit = async (data: LoginFormValues) => {
  setIsSubmitting(true);
  setAuthError("");
  try {
    if (loginMethod === 'email') {
      // Handle email login flow through handleEmailCheck
      await handleEmailCheck(data.email!, isOldUser ? data.contactNo : undefined);
    } else if (loginMethod === 'mobile') {
      // Handle mobile login flow
      const response = await loginUser({
        contactNo: data.contactNo!,
      });
      if (response.success === false) {
        setAuthError(response.message || 'Authentication failed');
        toast.error(response.message || 'Authentication failed');
        return;
      }
      const redirect = searchParams.get('redirect') || '';
      const redirectParam = redirect ? `&redirect=${encodeURIComponent(redirect)}` : '';
      router.push(`/verify-otp?contactNo=${data.contactNo}&flow=login${redirectParam}`);
      toast.success('OTP sent successfully. Please check your phone.');
      loginForm.reset({ contactNo: '', email: '' });
      setIsOldUser(false);
      setEmailData(null);
    }
  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || 'Something went wrong';
    setAuthError(errorMessage);
    toast.error(errorMessage);
  } finally {
    setIsSubmitting(false);
  }
};

  const onRegisterSubmit = async (data: RegisterFormValues) => {
    setIsSubmitting(true);
    setAuthError("");
    try {
      const registerData: RegisterFormValues = {
        ...data,
        ...(referralCode ? { referralCode } : {}),
      };
      const response = await registerUser(registerData);
      if (response.success === false) {
        setAuthError(response.message || 'Authentication failed');
        toast.error(response.message || 'Authentication failed');
        return;
      }
      const redirect = searchParams.get('redirect') || '';
      const redirectParam = redirect ? `&redirect=${encodeURIComponent(redirect)}` : '';
      router.push(`/verify-otp?contactNo=${data.contactNo}&flow=register&firstName=${encodeURIComponent(data.firstName)}&lastName=${encodeURIComponent(data.lastName)}${data.referralCode ? `&referralCode=${encodeURIComponent(data.referralCode)}` : ''}${data.email ? `&email=${encodeURIComponent(data.email)}` : ''}${redirectParam}`);
      toast.success('OTP sent successfully. Please check your phone.');
      registerForm.reset({ firstName: '', lastName: '', contactNo: '', referralCode: '', email: '' });
      localStorage.removeItem('referralCode');
      setReferralCode(null);
      setIsOldUser(false);
      setEmailData(null);
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Something went wrong';
      setAuthError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Header />
      <main className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-xl bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden">
          <div className="text-center mb-6">
            <div className="bg-orange-50 border border-orange-100 rounded-lg py-2 px-4 mb-4">
              <p className="text-center text-orange-700 font-medium">
                {isLogin ? "Coaching Classes Login Portal" : "Coaching Classes Registration Portal"}
              </p>
            </div>
            <div className="flex justify-center mb-4">
              <div className="flex bg-gray-100 rounded-lg p-1">
                <Button
                  variant={isLogin ? "default" : "ghost"}
                  className={`px-4 py-2 rounded-lg ${isLogin ? "bg-[#ff914d] text-white hover:bg-[#ff914d]/90" : "text-gray-600 hover:text-[#ff914d]"}`}
                  onClick={() => {
                    setIsLogin(true);
                    setAuthError("");
                    setLoginMethod('mobile');
                    setIsOldUser(false);
                    setEmailData(null);
                    loginForm.reset({ contactNo: '', email: '' });
                    loginForm.trigger();
                  }}
                >
                  Class Login
                </Button>
                <Button
                  variant={!isLogin ? "default" : "ghost"}
                  className={`px-4 py-2 rounded-lg ${!isLogin ? "bg-[#ff914d] text-white hover:bg-[#ff914d]/90" : "text-gray-600 hover:text-[#ff914d]"}`}
                  onClick={() => {
                    setIsLogin(false);
                    setAuthError("");
                    setLoginMethod('mobile');
                    setIsOldUser(false);
                    setEmailData(null);
                    registerForm.reset({ firstName: '', lastName: '', contactNo: '', referralCode: referralCode || '', email: '' });
                    registerForm.trigger();
                  }}
                >
                  Class Sign Up
                </Button>
              </div>
            </div>
            {isLogin && (
              <div className="flex justify-center mb-4">
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <Button
                    variant={loginMethod === 'mobile' ? "default" : "ghost"}
                    className={`px-4 py-2 rounded-lg ${loginMethod === 'mobile' ? "bg-[#ff914d] text-white hover:bg-[#ff914d]/90" : "text-gray-600 hover:text-[#ff914d]"}`}
                    onClick={() => {
                      setLoginMethod('mobile');
                      setAuthError("");
                      setIsOldUser(false);
                      setEmailData(null);
                      loginForm.reset({ contactNo: '', email: '' });
                      loginForm.trigger();
                    }}
                  >
                    Mobile
                  </Button>
                  <Button
                    variant={loginMethod === 'email' ? "default" : "ghost"}
                    className={`px-4 py-2 rounded-lg ${loginMethod === 'email' ? "bg-[#ff914d] text-white hover:bg-[#ff914d]/90" : "text-gray-600 hover:text-[#ff914d]"}`}
                    onClick={() => {
                      setLoginMethod('email');
                      setAuthError("");
                      setIsOldUser(false);
                      setEmailData(null);
                      loginForm.reset({ contactNo: '', email: '' });
                      loginForm.trigger();
                    }}
                  >
                    Email
                  </Button>
                </div>
              </div>
            )}
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {isLogin ? "Welcome Back to Your Class Portal" : "Register Your Coaching Class"}
            </h2>
            <div className="flex items-center justify-center">
              <div className="h-0.5 w-16 bg-orange-300 mr-3"></div>
              <span className="text-[#ff914d] font-medium">COACHING CLASS PORTAL</span>
              <div className="h-0.5 w-16 bg-orange-300 ml-3"></div>
            </div>
            {referralCode && !isLogin && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-700 text-center">
                  🎉 You&apos;re joining via referral code: <span className="font-semibold">{referralCode}</span>
                </p>
              </div>
            )}
          </div>
          <div>
            {isLogin ? (
              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-6">
                  {authError && <FormErrorAlert message={authError} />}
                  {loginMethod === 'email' && (
                    <FormField
                      control={loginForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">Email</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <FiMail className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]" size={20} />
                              <Input
                                type="email"
                                placeholder="Enter email address"
                                className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          {loginForm.formState.touchedFields.email && <FormMessage className="text-red-500" />}
                        </FormItem>
                      )}
                    />
                  )}
                  <AnimatePresence>
                    {(loginMethod === 'mobile' || isOldUser) && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <FormField
                          control={loginForm.control}
                          name="contactNo"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700 font-medium">Mobile Number</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <FiPhone className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]" size={20} />
                                  <Input
                                    type="tel"
                                    placeholder="Enter 10-digit mobile number"
                                    className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                    maxLength={10}
                                    {...field}
                                    disabled={!!emailData?.contactNo && !isOldUser && loginMethod === 'email'}
                                    autoFocus={isOldUser}
                                  />
                                </div>
                              </FormControl>
                              {loginForm.formState.touchedFields.contactNo && <FormMessage className="text-red-500" />}
                            </FormItem>
                          )}
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>
                  <Button
                    type="submit"
                    className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors"
                    disabled={isSubmitting || !loginForm.formState.isValid}
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : loginMethod === 'email' && !isOldUser ? (
                      'Check Email'
                    ) : (
                      'Send OTP to Login'
                    )}
                  </Button>
                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-500">
                      By continuing, you agree to our{' '}
                      <a href="https://www.uest.in/terms-and-conditions" className="text-[#ff914d] hover:underline">
                        Terms & Conditions
                      </a>{' '}
                      and{' '}
                      <a href="https://www.uest.in/privacy-policy" className="text-[#ff914d] hover:underline">
                        Privacy Policy
                      </a>
                    </p>
                  </div>
                </form>
              </Form>
            ) : (
              <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-6">
                  {authError && <FormErrorAlert message={authError} />}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <FormField
                      control={registerForm.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">First Name</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <FiUser className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]" size={20} />
                              <Input
                                placeholder="First Name"
                                className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          {registerForm.formState.touchedFields.firstName && <FormMessage className="text-red-500" />}
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={registerForm.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">Last Name</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <FiUser className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]" size={20} />
                              <Input
                                placeholder="Last Name"
                                className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          {registerForm.formState.touchedFields.lastName && <FormMessage className="text-red-500" />}
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={registerForm.control}
                    name="contactNo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">Mobile Number</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <FiPhone className="absolute left-3 top-1/2 -translate-y-1/2 text-[#ff914d]" size={20} />
                            <Input
                              type="tel"
                              placeholder="Enter 10-digit mobile number"
                              className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                              maxLength={10}
                              {...field}
                            />
                          </div>
                        </FormControl>
                        {registerForm.formState.touchedFields.contactNo && <FormMessage className="text-red-500" />}
                      </FormItem>
                    )}
                  />
                
                  {referralCode && (
                    <FormField
                      control={registerForm.control}
                      name="referralCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-gray-700 font-medium">Referral Code</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                placeholder="Referral Code"
                                className="pl-10 rounded-lg border-gray-200 bg-white focus:border-[#ff914d] focus:ring-[#ff914d]/20"
                                {...field}
                                disabled
                              />
                            </div>
                          </FormControl>
                          {registerForm.formState.touchedFields.referralCode && <FormMessage className="text-red-500" />}
                        </FormItem>
                      )}
                    />
                  )}
                  <Button
                    type="submit"
                    className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors"
                    disabled={isSubmitting || !registerForm.formState.isValid}
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      'Send OTP to Register'
                    )}
                  </Button>
                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-500">
                      By continuing, you agree to our{' '}
                      <a href="https://www.uest.in/terms-and-conditions" className="text-[#ff914d] hover:underline">
                        Terms & Conditions
                      </a>{' '}
                      and{' '}
                      <a href="https://www.uest.in/privacy-policy" className="text-[#ff914d] hover:underline">
                        Privacy Policy
                      </a>
                    </p>
                  </div>
                </form>
              </Form>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}

export default function ClassesLoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    }>
      <ClassesLoginPageContent />
    </Suspense>
  );
}