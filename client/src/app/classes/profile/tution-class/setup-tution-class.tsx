"use client";

import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import "react-datepicker/dist/react-datepicker.css";
import { MultiSelect } from "@/components/ui/multi-select";

import { axiosInstance } from "@/lib/axios";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { completeForm, FormId } from "@/store/slices/formProgressSlice";
import { fetchClassDetails } from "@/store/thunks/classThunks";

import { Trash2, PlusCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { safeParseArray } from "@/lib/helper";

// === Zod Schema ===
const tuitionClassSchema = z.object({
  tuitionDetails: z.array(
    z.object({
      education: z.string().min(1, "Category is required"),
      coachingType: z.array(z.string()).optional(),
      boardType: z.array(z.string()).optional(),
      subject: z.array(z.string()).optional(),
      medium: z.array(z.string()).optional(),
      section: z.array(z.string()).optional(),
      details: z.array(z.string()).optional(),
    })
  )
   .refine(
      (data) =>
        data.every((item) => {
          if (item.education === "Education") {
            return (
              item.boardType?.length &&
              item.subject?.length &&
              item.medium?.length &&
              item.section?.length
            );
          } else {
            return (
              !item.boardType?.length &&
              !item.subject?.length &&
              !item.medium?.length &&
              !item.section?.length
            );
          }
        }),
      {
        message:
          "For 'Education', boardType, subject, medium, and section are required. For other categories, these fields must be empty.",
        path: ["tuitionDetails"],
      }
    )
});

type FormValues = z.infer<typeof tuitionClassSchema>;

  type ConstantSubDetailValue = {
  id: string;
  name: string;
  isActive: boolean;
  subDetailId: string;
};

type ConstantSubDetail = {
  id: string;
  name: string;
  detailId: string;
  values: ConstantSubDetailValue[];
};

type Constant = {
  id: string;
  name: string;
  categoryId: string;
  subDetails: ConstantSubDetail[];
};


function TuitionDetailForm({
  form,
  index,
  removeTuition,
  constants,
  tuitionFieldsLength,
}: {
  form: any;
  index: number;
  removeTuition: (index: number) => void;
  constants: Constant[];
  tuitionFieldsLength: number;
}) {
  const getConstantValues = (categoryName: string, subDetailName: string) => {
    const category = constants.find((cat) => cat.name === categoryName);
    const subDetail = category?.subDetails.find((sub) => sub.name === subDetailName);
    return subDetail?.values || [];
  };

  const getEducationValues = (subDetailName: string) => {
    return getConstantValues("Education", subDetailName);
  };

  const getOtherCategoryValues = (categoryName: string) => {
    const category = constants.find((cat) => cat.name === categoryName);
    return category?.subDetails.map(subDetail => ({
      id: subDetail.id,
      name: subDetail.name,
      isActive: true,
      subDetailId: subDetail.id
    })) || [];
  };

  const educationValue = form.watch(`tuitionDetails.${index}.education`);

  return (
    <div className="relative rounded-2xl border p-6 bg-muted/40 shadow-sm space-y-6">
      {tuitionFieldsLength > 1 && (
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="absolute top-4 right-4 text-destructive hover:bg-destructive/10"
          onClick={() => removeTuition(index)}
        >
          <Trash2 size={20} />
        </Button>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Parent Category Dropdown */}
        <FormField
          control={form.control}
          name={`tuitionDetails.${index}.education`}
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel>Category</FormLabel>
              <FormControl>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.setValue(`tuitionDetails.${index}.boardType`, []);
                    form.setValue(`tuitionDetails.${index}.subject`, []);
                    form.setValue(`tuitionDetails.${index}.medium`, []);
                    form.setValue(`tuitionDetails.${index}.section`, []);
                    form.setValue(`tuitionDetails.${index}.details`, []);
                  }}
                  value={field.value}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {constants.map((cat) => (
                      <SelectItem key={cat.id} value={cat.name}>
                        {cat.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`tuitionDetails.${index}.coachingType`}
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel>Coaching Type</FormLabel>
              <FormControl>
                <MultiSelect
                  options={[
                    { label: "Personal", value: "Personal" },
                    { label: "Group", value: "Group" },
                    { label: "Online", value: "Online" },
                    { label: "Hybrid", value: "Hybrid" }
                  ]}
                  value={field.value || []}
                  onChange={(values: string[]) => field.onChange(values)}
                  placeholder="Select Coaching Type"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Education-specific Dropdowns */}
        {educationValue === "Education" && (
          <>
            <FormField
              control={form.control}
              name={`tuitionDetails.${index}.boardType`}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Board Type</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={getEducationValues("Board Type").map(item => ({
                        label: item.name,
                        value: item.name
                      }))}
                      value={field.value || []}
                      onChange={(values: string[]) => field.onChange(values)}
                      placeholder="Select Board Type"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`tuitionDetails.${index}.medium`}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Medium</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={getEducationValues("Medium").map(item => ({
                        label: item.name,
                        value: item.name
                      }))}
                      value={field.value || []}
                      onChange={(values: string[]) => field.onChange(values)}
                      placeholder="Select Medium"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`tuitionDetails.${index}.section`}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Section</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={getEducationValues("Section").map(item => ({
                        label: item.name,
                        value: item.name
                      }))}
                      value={field.value || []}
                      onChange={(values: string[]) => field.onChange(values)}
                      placeholder="Select Section"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`tuitionDetails.${index}.subject`}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Subject</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={getEducationValues("Subject").map(item => ({
                        label: item.name,
                        value: item.name
                      }))}
                      value={field.value || []}
                      onChange={(values: string[]) => field.onChange(values)}
                      placeholder="Select Subject"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {/* Non-Education Details Dropdown */}
        {educationValue && educationValue !== "Education" && (
          <FormField
            control={form.control}
            name={`tuitionDetails.${index}.details`}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>{educationValue} Details</FormLabel>
                <FormControl>
                  <MultiSelect
                    options={getOtherCategoryValues(educationValue).map(item => ({
                      label: item.name,
                      value: item.name
                    }))}
                    value={field.value || []}
                    onChange={(values: string[]) => field.onChange(values)}
                    placeholder={`Select ${educationValue} Details`}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </div>
    </div>
  );
}

// === Main Tuition Class Form Component ===
export function TuitionClassForm() {
  const dispatch = useDispatch();
  const [constants, setConstants] = useState<Constant[]>([]);
  const classData = useSelector((state: RootState) => state.class.classData);

  const form = useForm<FormValues>({
    resolver: zodResolver(tuitionClassSchema),
    defaultValues: {
      tuitionDetails: [
        {
          education: "",
          coachingType: [],
          boardType: [],
          subject: [],
          medium: [],
          section: [],
          details: [],
        },
      ],
    },
  });

  const {
    fields: tuitionFields,
    append: appendTuition,
    remove: removeTuition,
  } = useFieldArray({
    control: form.control,
    name: "tuitionDetails",
  });

  const fetchConstants = async () => {
    try {
      const response = await axiosInstance.get("/constant/TuitionClasses");
      if (response.data && response.data.details) {
        setConstants(response.data.details);
      }
    } catch {
      toast.error("Failed to fetch constants");
    }
  };

  const onSubmit = async (data: FormValues) => {
    try {
      // Convert arrays to strings for database storage
      const formattedData = {
        tuitionDetails: data.tuitionDetails.map(detail => ({
          ...detail,
          boardType: detail.boardType ? JSON.stringify(detail.boardType) : null,
          subject: detail.subject ? JSON.stringify(detail.subject) : null,
          medium: detail.medium ? JSON.stringify(detail.medium) : null,
          section: detail.section ? JSON.stringify(detail.section) : null,
          details: detail.details ? JSON.stringify(detail.details) : null,
        }))
      };

      await axiosInstance.post(
        `/classes-profile/tuition-classes`,
        formattedData
      );

      await dispatch(fetchClassDetails(classData.id));
      toast.success("Tuition class details uploaded successfully");
      dispatch(completeForm(FormId.TUTIONCLASS));
      form.reset({
        tuitionDetails: [
          {
            education: "",
            coachingType: [],
            boardType: [],
            subject: [],
            medium: [],
            section: [],
            details: [],
          },
        ],
      });
      toast.success("Now you can send for review your profile");
    } catch {
      toast.error("Something went wrong");
    }
  };

  const handleDeleteTuitionClass = async (tuitionId: string, classId: string) => {
    try {
      await axiosInstance.delete(`/classes-profile/tuition-class/${tuitionId}`, {
        data: { classId },
      });
      toast.success("Tuition class deleted successfully");
      await dispatch(fetchClassDetails(classId));

      // Reset form
      form.reset({
        tuitionDetails: [
          {
            education: "",
            coachingType: [],
            boardType: [],
            subject: [],
            medium: [],
            section: [],
            details: [],
          },
        ],
      });
    } catch {
      toast.error("Failed to delete tuition class");
    }
  };

  useEffect(() => {
    fetchConstants();
  }, []);

  return (
    <>
      {classData?.tuitionClasses?.length > 0 && (
        <div className="space-y-4 mb-6">
          <h3 className="text-lg font-semibold">Tuition Classes</h3>

          {classData.tuitionClasses.map((tuition: any, idx: number) => (
            <Card key={idx} className="bg-muted/30 relative">
              <CardHeader className="flex flex-row items-start justify-between">
                <CardTitle className="text-base font-semibold">
                  Tuition #{idx + 1}
                </CardTitle>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Delete Tuition Class</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to delete this tuition class? This
                        action cannot be undone. All associated time slots will
                        also be deleted.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="gap-2">
                      <Button
                        variant="outline"
                        onClick={() =>
                          (document.querySelector('button[data-state="open"]') as any)?.click()
                        }
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => {
                          handleDeleteTuitionClass(tuition.id, classData.id);
                          (document.querySelector('button[data-state="open"]') as any)?.click();
                        }}
                      >
                        Delete
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                {tuition.education && (
                  <div>
                    <span className="font-medium">Category:</span> {tuition.education}
                  </div>
                )}
                {tuition.coachingType && (
                  <div>
                    <span className="font-medium">Coaching Type:</span>{" "}
                    {safeParseArray(tuition.coachingType).join(", ")}
                  </div>
                )}
                {tuition.education === "Education" && (
                  <>
                    {tuition.boardType && (
                      <div>
                        <span className="font-medium">Board:</span>{" "}
                        {safeParseArray(tuition.boardType).join(", ")}
                      </div>
                    )}
                    {tuition.medium && (
                      <div>
                        <span className="font-medium">Medium:</span>{" "}
                        {safeParseArray(tuition.medium).join(", ")}
                      </div>
                    )}
                    {tuition.section && (
                      <div>
                        <span className="font-medium">Section:</span>{" "}
                        {safeParseArray(tuition.section).join(", ")}
                      </div>
                    )}
                    {tuition.subject && (
                      <div>
                        <span className="font-medium">Subject:</span>{" "}
                        {safeParseArray(tuition.subject).join(", ")}
                      </div>
                    )}
                  </>
                )}
                {tuition.education !== "Education" && tuition.details && (
                  <div>
                    <span className="font-medium">Details:</span>{" "}
                    {safeParseArray(tuition.details).join(", ")}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {tuitionFields.map((item, index) => (
            <TuitionDetailForm
              key={item.id}
              form={form}
              index={index}
              removeTuition={removeTuition}
              constants={constants}
              tuitionFieldsLength={tuitionFields.length}
            />
          ))}

          <Button
            type="button"
            onClick={() =>
              appendTuition({
                education: "",
                coachingType: [],
                boardType: [],
                subject: [],
                medium: [],
                section: [],
                details: [],
              })
            }
            className="flex items-center gap-2"
          >
            <PlusCircle size={18} />
            Add Another Tuition
          </Button>

          <Button type="submit">Save Tuition Class Details</Button>
        </form>
      </Form>
    </>
  );
}