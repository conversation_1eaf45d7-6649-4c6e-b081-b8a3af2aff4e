import React from 'react';

export const FilterInput = ({
  label,
  value,
  onChange,
}: {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => {
  const inputRef = React.useRef<HTMLInputElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.replace(/\s+/g, ' ').trimStart();
    e.target.value = newValue;
    onChange(e);
  };

  return (
    <div className="flex flex-col">
      <label className="text-sm font-medium text-muted-foreground mb-1">{label}</label>
      <input
        ref={inputRef}
        type="text"
        placeholder={`Enter ${label}`}
        className="border bg-white dark:bg-black rounded-lg px-3 py-2 text-sm text-black dark:text-white
                 focus:border-customOrange focus:ring focus:ring-customOrange/20 focus:outline-none
                 transition-all duration-200"
        value={value}
        onChange={handleChange}
        maxLength={50}
      />
    </div>
  );
};
