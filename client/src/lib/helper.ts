import { FormId, completeForm } from '@/store/slices/formProgressSlice';
import { AppDispatch } from '@/store';
import { jwtVerify } from 'jose';

/* eslint-disable */
export const evaluateCompletedForms = (classData: any, dispatch: AppDispatch) => {
  if (classData.contactNo) {
    dispatch(completeForm(FormId.PROFILE));
  }

  if (classData.ClassAbout?.tutorBio?.length > 50) {
    dispatch(completeForm(FormId.DESCRIPTION));
  }

  if (classData.ClassAbout?.profilePhoto && classData.ClassAbout?.classesLogo) {
    dispatch(completeForm(FormId.PHOTO_LOGO));
  }

  if (classData.education?.length > 0) {
    dispatch(completeForm(FormId.EDUCATION));
  }

  if (classData.certificates?.length > 0) {
    dispatch(completeForm(FormId.CERTIFICATES));
  }

  if (classData.experience?.length > 0) {
    dispatch(completeForm(FormId.EXPERIENCE));
  }

  if (classData.tuitionClasses?.length > 0) {
    dispatch(completeForm(FormId.TUTIONCLASS));
  }

  if (classData.address) {
    dispatch(completeForm(FormId.ADDRESS));
  }
};

export function convertTo24HourFormat(time12h: string): string {
  if (!time12h) return '';
  const [time, modifier] = time12h.split(' ');

  let [hours, minutes] = time.split(':');

  if (hours === '12') hours = '00';
  if (modifier === 'PM') hours = String(parseInt(hours) + 12);

  return `${hours.padStart(2, '0')}:${minutes}`;
}

export const safeParseArray = (value: any): string[] => {
  if (!value) return [];
  try {
    const parsed = typeof value === 'string' ? JSON.parse(value) : value;
    return Array.isArray(parsed) ? parsed : [parsed];
  } catch {
    return [value]; // fallback to treating as plain string
  }
};

export const parseAndJoinArray = (value: any): string => {
  try {
    const parsed = typeof value === 'string' ? JSON.parse(value) : value;
    return Array.isArray(parsed) ? parsed.join(', ') : parsed || 'N/A';
  } catch {
    return value || 'N/A';
  }
};


const secret = new TextEncoder().encode('secret123');

export async function verifyJWT(token: string): Promise<any | null> {
  try {
    const { payload } = await jwtVerify(token, secret);
    return payload;
  } catch (e) {
    console.error('Invalid token:', e);
    return null;
  }
}