import { Request, Response } from "express";
import prisma from "@/config/prismaClient";
import {
  checkUniqueEmail,
  checkUniqueUserName,
  createTuitionClass,
  saveCertificate,
  saveEducation,
  saveExperience,
  sendClassForReview,
  updateClassesProfileService,
  updateDescriptionQuery,
  updateProfileImages,
  deleteEducationRecord,
  deleteExperienceRecord,
  deleteCertificateRecord,
  deleteTuitionClassRecord,
  saveNoDegreeRecord,
  clearNoDegreeStatus,
  saveNoExperienceRecord,
  clearNoExperienceStatus,
  saveNoCertificateRecord,
  clearNoCertificateStatus,
  updateExperienceStatus,
  updateEducationStatus,
  updateCertificateStatus,


} from "../services/classesProfileService";
import * as path from 'path';
import * as fsPromises from 'fs/promises';
import { createAdminNotification } from '@/utils/notifications';
import { NotificationType } from '@prisma/client';

export const updateClassProfile = async (
  req: Request,
  res: Response
): Promise<any> => {
  const classId = req.class?.id;
  const { username } = req.body;

  if (!classId) {
    return res.status(400).json({ message: "Class ID is required" });
  }

  try {
    const existing = await checkUniqueUserName(username, classId)

    if (existing) {
      return res.status(400).json({ message: "Username already exists" });
    }

    const existingEmail = await checkUniqueEmail(req.body.email, classId);
    if (existingEmail) {
      return res.status(400).json({ message: "Email already exists" });
    }

    await updateClassesProfileService(req.body, classId);

    // Get class details for notification
    const classDetails = await prisma.classes.findUnique({
      where: { id: classId },
      select: { firstName: true, lastName: true, email: true }
    });

    // Create notification for admin about profile update
    await createAdminNotification({
      type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
      title: 'Class Profile Updated',
      message: `Class ${classDetails?.firstName} ${classDetails?.lastName} has updated their profile. Please review the changes.`,
      data: {
        classId: classId,
        className: `${classDetails?.firstName} ${classDetails?.lastName}`,
        email: classDetails?.email,
        updatedFields: Object.keys(req.body)
      }
    });

    res.status(200).json({ message: "Class profile updated successfully" });
  } catch (error: any) {
    console.error("Update error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export const updateExperience = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const classId = req.class?.id;
    const { experiences, noExperience } = req.body;

    // First, check if the user already has isExperience=false in the database
    const existingExperience = await prisma.classesExpereince.findFirst({
      where: {
        classId,
        isExperience: false
      }
    });

    if (noExperience === "true") {
      // Create a record indicating the user has no experience
      await saveNoExperienceRecord(classId);
      return res.status(200).json({ message: "No experience status saved" });
    } else if (existingExperience?.isExperience === false) {
      // If user is unchecking the "I don't have experience" box, clear the no-experience status
      await clearNoExperienceStatus(classId);
      // If no experience data is being submitted, just return success
      if (!experiences) {
        return res.status(200).json({ message: "No experience status cleared" });
      }
    }

    // If user has isExperience=false and is trying to add experience data, prevent it
    if (existingExperience?.isExperience === false && noExperience !== "true") {
      return res.status(400).json({
        message: "Cannot add experience data when 'I don't have experience' is selected. Please uncheck that option first."
      });
    }

    // Only process experience data if noExperience is false and experience data is provided
    if (experiences) {
      const parsedExe = JSON.parse(experiences);
      const files = req.files as Express.Multer.File[];
      const filePaths = files.map((file) => file.filename);

      await saveExperience(classId, parsedExe, filePaths);
      return res.status(201).json({ message: "Experience saved" });
    }

    res.status(200).json({ message: "No changes made" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};


export const postEducation = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const classId = req.class?.id;
    const { education, noDegree } = req.body;

    // First, check if the user already has isDegree=false in the database
    const existingEducation = await prisma.classesEducation.findFirst({
      where: {
        classId,
        isDegree: false
      }
    });

    if (noDegree === "true") {
      // Create a record indicating the user has no degree
      // This will delete any existing education records and create a single record with isDegree=false
      await saveNoDegreeRecord(classId);
      return res.status(200).json({ message: "No degree status saved" });
    } else if (existingEducation?.isDegree === false) {
      // If user is unchecking the "I don't have a degree" box, clear the no-degree status
      await clearNoDegreeStatus(classId);
      // If no education data is being submitted, just return success
      if (!education) {
        return res.status(200).json({ message: "No degree status cleared" });
      }
    }

    // If user has isDegree=false and is trying to add education data, prevent it
    if (existingEducation?.isDegree === false && noDegree !== "true") {
      return res.status(400).json({
        message: "Cannot add education data when 'I don't have a degree' is selected. Please uncheck that option first."
      });
    }

    // Only process education data if noDegree is false and education data is provided
    if (education) {
      const parsedEducation = JSON.parse(education);
      const files = req.files as Express.Multer.File[];
      const filePaths = files.map((file) => file.filename);

      await saveEducation(classId, parsedEducation, filePaths);
      return res.status(201).json({ message: "Education saved" });
    }

    res.status(200).json({ message: "No changes made" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};

export const postCertificate = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const classId = req.class?.id;
    const { certificates, noCertificates } = req.body;

    const existingCertificate = await prisma.classesCertificates.findFirst({
      where: {
        classId,
        isCertificate: false
      }
    });

    if (noCertificates === "true") {
      await saveNoCertificateRecord(classId);
      return res.status(200).json({ message: "No certificates status saved" });
    } else if (existingCertificate?.isCertificate === false) {
      await clearNoCertificateStatus(classId);
      if (!certificates) {
        return res.status(200).json({ message: "No certificates status cleared" });
      }
    }

    if (existingCertificate?.isCertificate === false && noCertificates !== "true") {
      return res.status(400).json({
        message: "Cannot add certificate data when 'I don't have certificates' is selected. Please uncheck that option first."
      });
    }

    if (certificates) {
      const parsedCertificates = JSON.parse(certificates);
      const files = req.files as Express.Multer.File[];
      const filePaths = files.map((file) => file.filename);

      await saveCertificate(classId, parsedCertificates, filePaths);
      return res.status(201).json({ message: "Certificate saved" });
    }

    res.status(200).json({ message: "No changes made" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};

export const updateProfilePhotos = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id || req.params?.classId;

    if (!classId) {
      return res.status(400).json({ message: "Class ID is required" });
    }

    const files = req.files as {
      profilePhoto?: Express.Multer.File[];
      classesLogo?: Express.Multer.File[];
    };
    const existingClass = await prisma.classesAbout.findUnique({ where: { classId } });

    const profilePhoto = files?.profilePhoto?.[0];
    const classesLogo = files?.classesLogo?.[0];

    // Handle null explicitly
    const profilePhotoUrl = profilePhoto
      ? `uploads/classes/${classId}/photos/${profilePhoto.filename}`
      : existingClass?.profilePhoto ?? undefined;

    const classesLogoUrl = classesLogo
      ? `uploads/classes/${classId}/photos/${classesLogo.filename}`
      : existingClass?.classesLogo ?? undefined;

    const updated = await updateProfileImages(classId, {
      profilePhoto: profilePhotoUrl,
      classesLogo: classesLogoUrl,
    });

    res.status(200).json({ message: "Images updated", data: updated });
  } catch (error) {
    console.error("Error uploading:", error);
    res.status(500).json({ message: "Upload failed" });
  }
};

export const updateDescription = async (req: Request, res: Response) => {
  try {
    const classId = req.class?.id;
    const { headline, description } = req.body;

    const updated = await updateDescriptionQuery(classId, {
      catchyHeadline: headline,
      tutorBio: description,
    });

    res.status(200).json({ message: "Description updated", data: updated });
  } catch (error) {
    console.error("Error updating description:", error);
    res.status(500).json({ message: "Update failed" });
  }
};



export const handleSendForReview = async (req: Request, res: Response) : Promise<any> => {
  const { id } = req.params;

  try {
    const result = await sendClassForReview(id);
    return res.status(200).json(result);
  } catch (error: any) {
    console.error("Send for review failed:", error);
    if (error.message === "Class not found") {
      return res.status(404).json({ message: error.message });
    }
    return res.status(500).json({ message: "Internal server error" });
  }
};

export const createTuitionClassController = async (req: Request, res: Response) : Promise<any> => {
  const classId = req.body.classId || req.class?.id;
  const tuitionClassData = req.body;

  if (!classId) {
    return res.status(400).json({ message: "Class ID is required" });
  }

  try {
    const createdClass = await createTuitionClass(classId, tuitionClassData);
    return res.status(201).json(createdClass);
  } catch {
    return res.status(500).json({ message: "Failed to create tuition class" });
  }
};

export const deleteProfileRecord = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id, type } = req.params;
    const { classId } = req.body;

    let result;
    const uploadFolder = path.join(__dirname, '../../../../uploads', 'classes', classId);

    const deleteFileIfExists = async (filePath: string) => {
      try {
        await fsPromises.access(filePath);
        await fsPromises.unlink(filePath);
      } catch {
        console.log(`File not found or already deleted: ${filePath}`);
      }
    };

    switch (type) {
      case 'education':
        result = await deleteEducationRecord(id);
        if (result?.certificate) {
          await deleteFileIfExists(path.join(uploadFolder, 'education', result.certificate));
        }
        break;

      case 'experience':
        result = await deleteExperienceRecord(id);
        if (result?.certificateUrl) {
          await deleteFileIfExists(path.join(uploadFolder, 'experience', result.certificateUrl));
        }
        break;

      case 'certificate':
        result = await deleteCertificateRecord(id);
        if (result?.certificateUrl) {
          await deleteFileIfExists(path.join(uploadFolder, 'certificates', result.certificateUrl));
        }
        break;

      case 'tuition-class':
        result = await deleteTuitionClassRecord(id);
        break;

      default:
        res.status(400).json({ message: "Invalid record type" });
        return;
    }

    res.status(200).json({ message: "Record deleted successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Failed to delete record" });
  }
};

// Admin Controllers

export const postExperienceByAdmin = async (req: Request, res: Response): Promise<any> => {
  try {
    const { classId } = req.params;
    const { experiences, noExperience } = req.body;

    if (!classId) {
      return res.status(400).json({ message: "Class ID is required" });
    }
    const existingExperience = await prisma.classesExpereince.findFirst({
      where: {
        classId,
        isExperience: false
      }
    });

    if (noExperience === "true") {
      await saveNoExperienceRecord(classId);
      return res.status(200).json({ message: "No experience status saved" });
    } else if (existingExperience?.isExperience === false) {
      await clearNoExperienceStatus(classId);
      if (!experiences) {
        return res.status(200).json({ message: "No experience status cleared" });
      }
    }

    if (existingExperience?.isExperience === false && noExperience !== "true") {
      return res.status(400).json({
        message: "Cannot add experience data when 'I don't have experience' is selected. Please uncheck that option first."
      });
    }

    if (experiences) {
      const parsedExperiences = JSON.parse(experiences);
      const files = req.files as Express.Multer.File[];
      const filePaths = files.map((file) => file.filename);

      await saveExperience(classId, parsedExperiences, filePaths);
      return res.status(201).json({ message: "Experience saved successfully" });
    }

    res.status(200).json({ message: "No changes made" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};

export const postCertificateByAdmin = async (req: Request, res: Response): Promise<any> => {
  try {
    const { classId } = req.params;
    const { certificates, noCertificates } = req.body;

    if (!classId) {
      return res.status(400).json({ message: "Class ID is required" });
    }

    const existingCertificate = await prisma.classesCertificates.findFirst({
      where: {
        classId,
        isCertificate: false
      }
    });

    if (noCertificates === "true") {
      await saveNoCertificateRecord(classId);
      return res.status(200).json({ message: "No certificates status saved" });
    } else if (existingCertificate?.isCertificate === false) {
      await clearNoCertificateStatus(classId);
      if (!certificates) {
        return res.status(200).json({ message: "No certificates status cleared" });
      }
    }

    if (existingCertificate?.isCertificate === false && noCertificates !== "true") {
      return res.status(400).json({
        message: "Cannot add certificate data when 'I don't have certificates' is selected. Please uncheck that option first."
      });
    }

    if (certificates) {
      const parsedCertificates = JSON.parse(certificates);
      const files = req.files as Express.Multer.File[];
      const filePaths = files.map((file) => file.filename);

      await saveCertificate(classId, parsedCertificates, filePaths);
      return res.status(201).json({ message: "Certificate saved successfully" });
    }

    res.status(200).json({ message: "No changes made" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};

export const postEducationByAdmin = async (req: Request, res: Response): Promise<any> => {
  try {
    const { classId } = req.params;
    const { education, noDegree } = req.body;

    if (!classId) {
      return res.status(400).json({ message: "Class ID is required" });
    }

    const existingEducation = await prisma.classesEducation.findFirst({
      where: {
        classId,
        isDegree: false
      }
    });

    if (noDegree === "true") {
      await saveNoDegreeRecord(classId);
      return res.status(200).json({ message: "No degree status saved" });
    } else if (existingEducation?.isDegree === false) {
      await clearNoDegreeStatus(classId);
      if (!education) {
        return res.status(200).json({ message: "No degree status cleared" });
      }
    }

    if (existingEducation?.isDegree === false && noDegree !== "true") {
      return res.status(400).json({
        message: "Cannot add education data when 'I don't have a degree' is selected. Please uncheck that option first."
      });
    }

    if (education) {
      const parsedEducation = JSON.parse(education);
      const files = req.files as Express.Multer.File[];
      const filePaths = files.map((file) => file.filename);

      await saveEducation(classId, parsedEducation, filePaths);
      return res.status(201).json({ message: "Education saved successfully" });
    }

    res.status(200).json({ message: "No changes made" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};

export const createAddressClassController = async (req: Request, res: Response): Promise<any> => {
  const { fullAddress, city, state, postcode, country, latitude, longitude } = req.body;
  const classId = req.class?.id;

  if (!classId) {
    return res.status(400).json({ error: 'Class ID is required' });
  }

  try {
    const location = await prisma.classesAddress.upsert({
      where: { classId },
      update: {
        fullAddress,
        city,
        state,
        postcode,
        country,
        latitude,
        longitude,
      },
      create: {
        fullAddress,
        city,
        state,
        postcode,
        country,
        latitude,
        longitude,
        classId,
      },
    });

    res.json({ success: true, data: location });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Something went wrong' });
  }
};

export const updateExperienceStatusController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!id || !status) {
      return res.status(400).json({ message: "ID and status are required" });
    }

    if (status !== 'PENDING' && status !== 'APPROVED' && status !== 'REJECTED') {
      return res.status(400).json({ message: "Invalid status value" });
    }

    const updatedExperience = await updateExperienceStatus(id, status);
    res.status(200).json({
      message: "Experience status updated successfully",
      data: updatedExperience
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};

export const updateEducationStatusController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!id || !status) {
      return res.status(400).json({ message: "ID and status are required" });
    }

    if (status !== 'PENDING' && status !== 'APPROVED' && status !== 'REJECTED') {
      return res.status(400).json({ message: "Invalid status value" });
    }

    const updatedEducation = await updateEducationStatus(id, status);
    res.status(200).json({
      message: "Education status updated successfully",
      data: updatedEducation
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};

export const updateCertificateStatusController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!id || !status) {
      return res.status(400).json({ message: "ID and status are required" });
    }

    if (status !== 'PENDING' && status !== 'APPROVED' && status !== 'REJECTED') {
      return res.status(400).json({ message: "Invalid status value" });
    }

    const updatedCertificate = await updateCertificateStatus(id, status);
    res.status(200).json({
      message: "Certificate status updated successfully",
      data: updatedCertificate
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Server Error" });
  }
};



