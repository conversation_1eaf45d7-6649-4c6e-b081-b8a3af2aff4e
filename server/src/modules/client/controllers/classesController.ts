import { Request, Response } from 'express';
import {
  fetchAllClasses,
  fetchClassDetailsById,
  fetchClassDetailsByIdForAdmin,
  getCategoryCounts,
  getApprovedTutors,
  updateStatusClass,
  fetchAllClassesforCount,
  updateClassByAdminService,
  deleteClassService,
  getNearbyClasses,
} from '../services/classesService';
import { createNotification } from '@/utils/notifications';
import { UserType, NotificationType } from '@prisma/client';

export const getClassDetailsById = async (req: Request, res: Response): Promise<any> => {
  const classId = req.params?.id;

  const isAdmin = req.originalUrl.includes('/admin');

  try {
    let classData;

    if (isAdmin) {
      const { id } = req.params;
      classData = await fetchClassDetailsByIdForAdmin(id);
    } else {
      if (!classId) {
        return res.status(400).json({ message: 'Class ID is required' });
      }
      classData = await fetchClassDetailsById(classId);
    }

    if (!classData) {
      return res.status(404).json({ message: 'Class not found' });
    }

    res.status(200).json(classData);
  } catch (error) {
    console.error('Error fetching class with relations:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

export const getAllClasses = async (req: Request, res: Response): Promise<any> => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string | undefined;
  const status = req.query.status as string | undefined;

  try {
    const result = await fetchAllClasses(page, limit, search, status);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error fetching paginated classes:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

export const getAllClassesforCount = async (req: Request, res: Response) => {
  try {
    const result = await fetchAllClassesforCount();
    res.status(200).json(result);
  } catch (error) {
    console.error('Error fetching classes:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

export const getAllClassesLogos = async (req: Request, res: Response): Promise<any> => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string | undefined;
  const status = req.query.status as string | undefined;

  try {
    const result = await fetchAllClasses(page, limit, search, status);

    // Map the result to include only className, classId, and classesLogo
    const filteredClasses = result.classes.map((cls: any) => ({
      className: cls.className,
      classId: cls.id,
      classesLogo: cls.ClassAbout?.classesLogo || null,
    }));

    // Return the modified response
    res.status(200).json({
      classes: filteredClasses,
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    });
  } catch (error) {
    console.error('Error fetching paginated classes:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

export const patchClassStatus = async (req: Request, res: Response) => {
  const { classId } = req.params;
  const { status } = req.body;

  try {
    const updated = await updateStatusClass(classId, status);

    // Create notification for class
    if (status === 'APPROVED' || status === 'REJECTED') {
      const notificationType = status === 'APPROVED' ? NotificationType.CLASS_PROFILE_APPROVED : NotificationType.CLASS_PROFILE_REJECTED;
      const title = status === 'APPROVED' ? 'Profile Approved!' : 'Profile Rejected';
      const message = status === 'APPROVED'
        ? 'Your profile has been approved by admin. You can now access all features.'
        : 'Your profile has been rejected by admin. Please update your profile and resubmit.';

      await createNotification({
        userId: classId,
        userType: UserType.CLASS,
        type: notificationType,
        title,
        message,
        data: { status }
      });
    }

    res.status(200).json({ success: true, data: updated });
  } catch (err: any) {
    console.error('Error updating class status:', err);
    res.status(400).json({ success: false, message: err.message });
  }
};

export const deleteClass = async (req: Request, res: Response): Promise<any> => {
  const { classId } = req.params;

  try {
    const classData = await fetchClassDetailsByIdForAdmin(classId);
    if (!classData) {
      res.status(404).json({ success: false, message: 'Class not found' });
      return;
    }
    if (classData.status?.status === 'APPROVED') {
      res.status(403).json({ success: false, message: 'Cannot delete approved class' });
      return;
    }
    await deleteClassService(classId);
    res.status(200).json({ success: true, message: 'Class deleted successfully' });
  } catch (err: any) {
    res.status(400).json({ success: false, message: err.message || 'Failed to delete class' });
  }
};

export const getCategoryCountsController = async (req: Request, res: Response) => {
  try {
    const categoryCounts = await getCategoryCounts();
    res.json(categoryCounts);
  } catch (err) {
    console.error('Error fetching category counts', err);
    res.status(500).json({ message: 'Something went wrong' });
  }
};

export const updateClassByAdmin = async (req: Request, res: Response): Promise<any> => {
  const { id } = req.params;
  const updateData = req.body;

  try {
    const updatedClass = await updateClassByAdminService(id, updateData);
    res.status(200).json({
      success: true,
      message: 'Class updated successfully',
      data: updatedClass
    });
  } catch (err: any) {
    console.error('Error updating class:', err);
    res.status(400).json({
      success: false,
      message: err.message || 'Failed to update class'
    });
  }
};

export const listApprovedTutors = async (req: Request, res: Response) => {
  try {
    const sortByRating = req.query.sortByRating === 'true';
    const sortByReviewCount = req.query.sortByReviewCount === 'true';

    const filters = {
      page: Number(req.query.page) || 1,
      limit: Number(req.query.limit) || 10,
      firstName: req.query.firstName as string,
      lastName: req.query.lastName as string,
      className: req.query.className as string,
      boardType: req.query.boardType as string,
      medium: req.query.medium as string,
      section: req.query.section as string,
      subject: req.query.subject as string,
      coachingType: req.query.coachingType as string,
      education: req.query.education as string,
      details: req.query.details as string,
      sortByRating,
      sortByReviewCount,
    };

    const result = await getApprovedTutors(filters);
    res.json(result);
  } catch (err) {
    console.error('Error fetching tutors', err);
    res.status(500).json({ message: 'Something went wrong' });
  }
};

export const getNearbyClassesController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { lat, lng, radius } = req.query;
    if (!lat || !lng || !radius) {
      res.status(400).json({ error: 'lat, lng, and radius are required' });
      return;
    }
    const userLat = parseFloat(lat as string);
    const userLng = parseFloat(lng as string);
    const userRadius = parseFloat(radius as string);
    if (isNaN(userLat) || isNaN(userLng) || isNaN(userRadius)) {
      res.status(400).json({ error: 'lat, lng, and radius must be numbers' });
      return;
    }
    const classes = await getNearbyClasses(userLat, userLng, userRadius);
    res.json({ success: true, data: classes });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to fetch nearby classes' });
  }
};